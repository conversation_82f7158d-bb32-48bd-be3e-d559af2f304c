// Database Connection Test Script
import { createClient } from '@supabase/supabase-js';
import { InfluxDB, Point } from '@influxdata/influxdb-client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🧪 Testing Database Connections...\n');

// Test configuration
const testConfig = {
  supabase: {
    url: process.env.SUPABASE_URL,
    serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
    anonKey: process.env.SUPABASE_ANON_KEY
  },
  influxdb: {
    url: process.env.INFLUXDB_URL || 'http://localhost:8086',
    token: process.env.INFLUXDB_TOKEN,
    org: process.env.INFLUXDB_ORG || 'mev-arbitrage-org',
    bucket: process.env.INFLUXDB_BUCKET || 'mev-arbitrage-metrics'
  }
};

// Test Supabase Connection
async function testSupabaseConnection() {
  console.log('📊 Testing Supabase Connection...');
  
  try {
    // Check if configuration exists
    if (!testConfig.supabase.url || !testConfig.supabase.serviceKey) {
      console.log('⚠️  Supabase configuration not found in environment variables');
      console.log('   Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
      return false;
    }

    // Create Supabase client
    const supabase = createClient(
      testConfig.supabase.url,
      testConfig.supabase.serviceKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    console.log('✅ Supabase client created successfully');
    console.log(`   URL: ${testConfig.supabase.url}`);

    // Test basic connection with a simple query
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1);

    if (error) {
      console.log('❌ Supabase connection failed:', error.message);
      return false;
    }

    console.log('✅ Supabase connection successful');

    // Test if our custom tables exist
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['trades', 'opportunities', 'performance_metrics', 'tokens']);

    if (tablesError) {
      console.log('⚠️  Could not check custom tables:', tablesError.message);
    } else {
      const tableNames = tables.map(t => t.table_name);
      console.log('📋 Custom tables found:', tableNames.length > 0 ? tableNames.join(', ') : 'None');
      
      if (tableNames.length === 0) {
        console.log('💡 Run the SQL schema from database/supabase-schema.sql to create tables');
      }
    }

    // Test write operation (insert a test record)
    const testRecord = {
      opportunity_id: 'test-' + Date.now(),
      type: 'intra-chain',
      assets: ['ETH', 'USDC'],
      exchanges: ['Uniswap', 'Sushiswap'],
      potential_profit: 100.50,
      profit_percentage: 2.5,
      timestamp: new Date().toISOString(),
      network: 'ethereum',
      confidence: 85.5,
      slippage: 0.1
    };

    if (tableNames.includes('opportunities')) {
      const { error: insertError } = await supabase
        .from('opportunities')
        .insert([testRecord]);

      if (insertError) {
        console.log('⚠️  Test write failed:', insertError.message);
      } else {
        console.log('✅ Test write successful');
        
        // Clean up test record
        await supabase
          .from('opportunities')
          .delete()
          .eq('opportunity_id', testRecord.opportunity_id);
        console.log('🧹 Test record cleaned up');
      }
    }

    return true;

  } catch (error) {
    console.log('❌ Supabase test failed:', error.message);
    return false;
  }
}

// Test InfluxDB Connection
async function testInfluxDBConnection() {
  console.log('\n📈 Testing InfluxDB Connection...');
  
  try {
    // Check if configuration exists
    if (!testConfig.influxdb.url) {
      console.log('⚠️  InfluxDB URL not configured, using default: http://localhost:8086');
    }

    if (!testConfig.influxdb.token) {
      console.log('⚠️  InfluxDB token not found in environment variables');
      console.log('   Please set INFLUXDB_TOKEN');
      return false;
    }

    // Create InfluxDB client
    const influxDB = new InfluxDB({
      url: testConfig.influxdb.url,
      token: testConfig.influxdb.token,
    });

    console.log('✅ InfluxDB client created successfully');
    console.log(`   URL: ${testConfig.influxdb.url}`);
    console.log(`   Org: ${testConfig.influxdb.org}`);
    console.log(`   Bucket: ${testConfig.influxdb.bucket}`);

    // Test write API
    const writeApi = influxDB.getWriteApi(
      testConfig.influxdb.org,
      testConfig.influxdb.bucket,
      'ns'
    );

    // Create a test data point
    const testPoint = new Point('connection_test')
      .tag('source', 'test-script')
      .tag('environment', process.env.NODE_ENV || 'development')
      .floatField('value', 1.0)
      .timestamp(new Date());

    // Write test point
    writeApi.writePoint(testPoint);
    await writeApi.flush();
    console.log('✅ Test write successful');

    // Test query API
    const queryApi = influxDB.getQueryApi(testConfig.influxdb.org);
    
    const query = `
      from(bucket: "${testConfig.influxdb.bucket}")
        |> range(start: -1h)
        |> filter(fn: (r) => r._measurement == "connection_test")
        |> limit(n: 1)
    `;

    let queryResult = [];
    await queryApi.queryRows(query, {
      next(row, tableMeta) {
        const record = tableMeta.toObject(row);
        queryResult.push(record);
      },
      error(error) {
        console.log('⚠️  Query test failed:', error.message);
      },
      complete() {
        console.log('✅ Test query successful');
        if (queryResult.length > 0) {
          console.log('📊 Sample data point retrieved');
        }
      },
    });

    // Close connections
    await writeApi.close();
    
    return true;

  } catch (error) {
    console.log('❌ InfluxDB test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 Make sure InfluxDB is running on', testConfig.influxdb.url);
      console.log('   Start InfluxDB: influxd');
    } else if (error.message.includes('unauthorized')) {
      console.log('💡 Check your INFLUXDB_TOKEN and organization settings');
    }
    
    return false;
  }
}

// Test Backend Service Integration
async function testBackendIntegration() {
  console.log('\n🔧 Testing Backend Service Integration...');
  
  try {
    // Test if backend is running
    const response = await fetch('http://localhost:3001/health');
    
    if (!response.ok) {
      console.log('⚠️  Backend server not responding');
      console.log('   Start backend: npm run dev:backend');
      return false;
    }

    const healthData = await response.json();
    console.log('✅ Backend server is running');
    console.log('📊 Service status:', healthData.services);

    // Test database-related endpoints
    const endpoints = [
      '/api/opportunities',
      '/api/tokens',
      '/api/analytics/performance'
    ];

    for (const endpoint of endpoints) {
      try {
        const testResponse = await fetch(`http://localhost:3001${endpoint}`);
        const testData = await testResponse.json();
        
        if (testResponse.ok && testData.success) {
          console.log(`✅ ${endpoint}: ${testData.data?.length || 0} records`);
        } else {
          console.log(`⚠️  ${endpoint}: ${testData.message || 'No data'}`);
        }
      } catch (error) {
        console.log(`❌ ${endpoint}: ${error.message}`);
      }
    }

    return true;

  } catch (error) {
    console.log('❌ Backend integration test failed:', error.message);
    return false;
  }
}

// Main test function
async function runConnectionTests() {
  console.log('🚀 MEV Arbitrage Bot - Database Connection Tests\n');
  
  const results = {
    supabase: false,
    influxdb: false,
    backend: false
  };

  // Run tests
  results.supabase = await testSupabaseConnection();
  results.influxdb = await testInfluxDBConnection();
  results.backend = await testBackendIntegration();

  // Summary
  console.log('\n📋 Connection Test Summary:');
  console.log('================================');
  console.log(`Supabase:  ${results.supabase ? '✅ Connected' : '❌ Failed'}`);
  console.log(`InfluxDB:  ${results.influxdb ? '✅ Connected' : '❌ Failed'}`);
  console.log(`Backend:   ${results.backend ? '✅ Running' : '❌ Not Running'}`);
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  
  console.log(`\nOverall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All database connections are working perfectly!');
  } else {
    console.log('⚠️  Some connections need attention. Check the logs above.');
  }

  // Next steps
  console.log('\n📚 Next Steps:');
  if (!results.supabase) {
    console.log('• Set up Supabase: Follow docs/DATABASE_SETUP.md');
  }
  if (!results.influxdb) {
    console.log('• Set up InfluxDB: Follow docs/DATABASE_SETUP.md');
  }
  if (!results.backend) {
    console.log('• Start backend: npm run dev:backend');
  }
  if (passedTests === totalTests) {
    console.log('• Your system is ready for production! 🚀');
  }
}

// Run the tests
runConnectionTests().catch(console.error);
