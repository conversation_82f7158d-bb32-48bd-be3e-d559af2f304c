const API_BASE_URL = 'http://localhost:3001/api';
const WS_URL = 'ws://localhost:3001';

export interface BackendOpportunity {
  id: string;
  type: string;
  assets: string[];
  exchanges: string[];
  potentialProfit: number;
  profitPercentage: number;
  timestamp: number;
  network: string;
  confidence: number;
  slippage: number;
}

export interface BackendTrade {
  id: string;
  type: string;
  assets: string[];
  exchanges: string[];
  executedProfit: number;
  gasFees: number;
  status: string;
  timestamp: number;
  network: string;
  txHash?: string;
}

export interface BackendToken {
  id: string;
  name: string;
  symbol: string;
  address: string;
  liquidity: number;
  safetyScore: number;
  isWhitelisted: boolean;
  network: string;
  decimals: number;
  totalSupply: string;
  lastUpdated: number;
}

export interface BackendMetrics {
  totalTrades: number;
  successfulTrades: number;
  totalProfit: number;
  netProfit: number;
  winRate: number;
  avgProfit: number;
  dailyVolume: number;
}

export interface BackendSystemHealth {
  isHealthy: boolean;
  emergencyStop: boolean;
  riskMetrics: any;
  activeAlerts: number;
  criticalAlerts: number;
}

class BackendService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;

  // API Methods
  async fetchOpportunities(limit = 20): Promise<BackendOpportunity[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/opportunities?limit=${limit}`);
      const result = await response.json();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error fetching opportunities:', error);
      return [];
    }
  }

  async fetchTrades(limit = 50): Promise<BackendTrade[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/trades?limit=${limit}`);
      const result = await response.json();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error fetching trades:', error);
      return [];
    }
  }

  async fetchTokens(): Promise<BackendToken[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/tokens`);
      const result = await response.json();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error fetching tokens:', error);
      return [];
    }
  }

  async fetchMetrics(): Promise<BackendMetrics | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/analytics/performance`);
      const result = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Error fetching metrics:', error);
      return null;
    }
  }

  async fetchSystemHealth(): Promise<BackendSystemHealth | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/system/health`);
      const result = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Error fetching system health:', error);
      return null;
    }
  }

  async addTokenToWhitelist(token: {
    name: string;
    symbol: string;
    address: string;
    network: string;
    decimals?: number;
  }): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/tokens/whitelist`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(token),
      });
      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('Error adding token to whitelist:', error);
      return false;
    }
  }

  async addTokenToBlacklist(token: {
    address: string;
    network: string;
    reason: string;
  }): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/tokens/blacklist`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(token),
      });
      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('Error adding token to blacklist:', error);
      return false;
    }
  }

  async toggleEmergencyStop(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/system/emergency-stop/toggle`, {
        method: 'POST',
      });
      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('Error toggling emergency stop:', error);
      return false;
    }
  }

  // WebSocket Methods
  initWebSocket(callbacks: {
    onOpportunity?: (opportunity: BackendOpportunity) => void;
    onTrade?: (trade: BackendTrade) => void;
    onSystemUpdate?: (data: any) => void;
    onConnect?: () => void;
    onDisconnect?: () => void;
    onError?: (error: Event) => void;
  }) {
    try {
      this.ws = new WebSocket(WS_URL);

      this.ws.onopen = () => {
        console.log('WebSocket connected to backend');
        this.reconnectAttempts = 0;

        // Subscribe to channels
        this.ws?.send(JSON.stringify({ type: 'subscribe', channel: 'opportunities' }));
        this.ws?.send(JSON.stringify({ type: 'subscribe', channel: 'trades' }));
        this.ws?.send(JSON.stringify({ type: 'subscribe', channel: 'system' }));

        callbacks.onConnect?.();
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);

          if (message.type === 'broadcast') {
            switch (message.channel) {
              case 'opportunity':
                callbacks.onOpportunity?.(message.data);
                break;
              case 'trade':
                callbacks.onTrade?.(message.data);
                break;
              case 'system':
                callbacks.onSystemUpdate?.(message.data);
                break;
            }
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected from backend');
        callbacks.onDisconnect?.();
        this.attemptReconnect(callbacks);
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        callbacks.onError?.(error);
      };

    } catch (error) {
      console.error('Error initializing WebSocket:', error);
    }
  }

  private attemptReconnect(callbacks: any) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      setTimeout(() => {
        this.initWebSocket(callbacks);
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  closeWebSocket() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  sendWebSocketMessage(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  // Health check
  async isBackendHealthy(): Promise<boolean> {
    try {
      const response = await fetch(`http://localhost:3001/health`, {
        method: 'GET',
        timeout: 5000,
      } as any);
      return response.ok;
    } catch (error) {
      return false;
    }
  }
}

export const backendService = new BackendService();
