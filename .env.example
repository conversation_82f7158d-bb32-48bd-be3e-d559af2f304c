# MEV Arbitrage Bot Environment Configuration
# Copy this file to .env and fill in your actual values

# ================================
# SERVER CONFIGURATION
# ================================
PORT=3001
NODE_ENV=development

# ================================
# DATABASE CONFIGURATION
# ================================
REDIS_URL=redis://localhost:6379

# ================================
# SUPABASE CONFIGURATION
# ================================
# Get these from your Supabase project dashboard
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_JWT_SECRET=your-jwt-secret-32-characters-long
SUPABASE_DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.your-project-ref.supabase.co:5432/postgres

# ================================
# INFLUXDB CONFIGURATION
# ================================
# For time-series metrics and analytics
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token-here
INFLUXDB_ORG=mev-arbitrage-org
INFLUXDB_BUCKET=mev-arbitrage-metrics
INFLUXDB_USERNAME=admin
INFLUXDB_PASSWORD=your-secure-password

# ================================
# BLOCKCHAIN CONFIGURATION
# ================================
# For local development (Hardhat)
ETHEREUM_RPC_URL=http://127.0.0.1:8545
POLYGON_RPC_URL=http://127.0.0.1:8545
BSC_RPC_URL=http://127.0.0.1:8545
SOLANA_RPC_URL=http://127.0.0.1:8545

# For production (replace with your actual RPC URLs)
# ETHEREUM_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/YOUR_API_KEY
# POLYGON_RPC_URL=https://polygon-mainnet.alchemyapi.io/v2/YOUR_API_KEY
# BSC_RPC_URL=https://bsc-dataseed.binance.org/
# SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# ================================
# SMART CONTRACT ADDRESSES
# ================================
# These are set automatically after deployment
TOKEN_DISCOVERY_ADDRESS=******************************************
LIQUIDITY_CHECKER_ADDRESS=******************************************
ARBITRAGE_EXECUTOR_ADDRESS=******************************************
GOVERNANCE_ADDRESS=******************************************

# ================================
# PRIVATE KEYS (NEVER commit real private keys)
# ================================
# Local Hardhat account (for testing only)
PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
# For production, use a secure private key
# PRIVATE_KEY=0x1234567890abcdef...
FLASHBOTS_PRIVATE_KEY=0x1234567890abcdef...

# ================================
# EXTERNAL API KEYS
# ================================
# Price data and blockchain APIs
COINGECKO_API_KEY=CG-your-api-key-here
ETHERSCAN_API_KEY=YourEtherscanApiKey
POLYGONSCAN_API_KEY=YourPolygonscanApiKey
BSCSCAN_API_KEY=YourBscscanApiKey
ALCHEMY_API_KEY=your-alchemy-api-key
INFURA_PROJECT_ID=your-infura-project-id
MORALIS_API_KEY=your-moralis-api-key
CHAINLINK_API_KEY=your-chainlink-api-key
PYTH_API_KEY=your-pyth-api-key

# ================================
# FLASHBOTS CONFIGURATION
# ================================
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
FLASHBOTS_BUILDER_URL=https://builder.flashbots.net

# ================================
# TRADING CONFIGURATION
# ================================
MIN_PROFIT_THRESHOLD=50
MAX_POSITION_SIZE=10000
MAX_SLIPPAGE=0.5
GAS_PRICE_MULTIPLIER=1.1

# ================================
# RISK MANAGEMENT
# ================================
EMERGENCY_STOP=false
MAX_DAILY_LOSS=1000
POSITION_SIZE_PERCENTAGE=2

# ================================
# NOTIFICATIONS
# ================================
# Telegram Bot for alerts
TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=-1001234567890

# Email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ALERT_EMAIL=<EMAIL>

# ================================
# SECURITY
# ================================
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_MAX=100
CORS_ORIGIN=http://localhost:5173,http://localhost:3000

# ================================
# MONITORING
# ================================
LOG_LEVEL=info
ENABLE_METRICS=true
