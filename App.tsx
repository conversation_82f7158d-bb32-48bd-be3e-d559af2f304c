
import React, { useState } from 'react';
import { Layout } from './components/Layout';
import { DashboardView } from './components/views/DashboardView';
import { OpportunitiesView } from './components/views/OpportunitiesView';
import { TradesView } from './components/views/TradesView';
import { TokensView } from './components/views/TokensView';
import { SystemStatusView } from './components/views/SystemStatusView';
import { StrategyInsightsView } from './components/views/StrategyInsightsView';
import { RiskManagementView } from './components/views/RiskManagementView';
import { ConfigurationView } from './components/views/ConfigurationView';
import { useBackendIntegration } from './hooks/useBackendIntegration';
import { ViewId, SystemComponent, SystemComponentType, ComponentStatus } from './types';

const App: React.FC = () => {
  const [currentView, setCurrentView] = useState<ViewId>(ViewId.DASHBOARD);
  const [isEmergencyStopActive, setIsEmergencyStopActive] = useState(false);

  const {
    tokens,
    opportunities,
    trades,
    metrics: kpis,
    isConnected,
    isLoading,
    error,
    backendHealthy,
    toggleEmergencyStop: backendToggleEmergencyStop,
    addTokenToWhitelist
  } = useBackendIntegration();

  // System components based on backend health and connection status
  const systemComponents: SystemComponent[] = [
    {
      id: 'sc1',
      name: 'ArbitrageExecutor.sol',
      type: SystemComponentType.SMART_CONTRACT,
      status: backendHealthy ? ComponentStatus.ONLINE : ComponentStatus.OFFLINE
    },
    {
      id: 'sc2',
      name: 'LiquidityChecker.sol',
      type: SystemComponentType.SMART_CONTRACT,
      status: backendHealthy ? ComponentStatus.ONLINE : ComponentStatus.OFFLINE
    },
    {
      id: 'bs1',
      name: 'Price Feed Service',
      type: SystemComponentType.BACKEND_SERVICE,
      status: backendHealthy ? ComponentStatus.ONLINE : ComponentStatus.OFFLINE
    },
    {
      id: 'bs2',
      name: 'Opportunity Detection',
      type: SystemComponentType.BACKEND_SERVICE,
      status: backendHealthy && opportunities.length > 0 ? ComponentStatus.ONLINE : ComponentStatus.WARNING
    },
    {
      id: 'bs3',
      name: 'Execution Service',
      type: SystemComponentType.BACKEND_SERVICE,
      status: backendHealthy ? ComponentStatus.ONLINE : ComponentStatus.OFFLINE
    },
    {
      id: 'dl1',
      name: 'Backend API',
      type: SystemComponentType.DATA_LAYER,
      status: isConnected ? ComponentStatus.ONLINE : ComponentStatus.OFFLINE
    },
    {
      id: 'dl2',
      name: 'WebSocket Connection',
      type: SystemComponentType.DATA_LAYER,
      status: isConnected ? ComponentStatus.ONLINE : ComponentStatus.DEGRADED
    },
    {
      id: 'ext1',
      name: 'Local Hardhat Network',
      type: SystemComponentType.EXTERNAL_INTEGRATION,
      status: backendHealthy ? ComponentStatus.ONLINE : ComponentStatus.OFFLINE
    },
  ];

  const handleToggleEmergencyStop = () => {
    setIsEmergencyStopActive(prev => !prev);
    backendToggleEmergencyStop();
  };

  const setTokens = (tokens: any) => {
    // This would be handled by the backend integration
    console.log('Setting tokens:', tokens);
  };

  const renderView = () => {
    switch (currentView) {
      case ViewId.DASHBOARD:
        return <DashboardView kpis={kpis} recentTrades={trades} recentOpportunities={opportunities} />;
      case ViewId.OPPORTUNITIES:
        return <OpportunitiesView opportunities={opportunities} />;
      case ViewId.TRADES:
        return <TradesView trades={trades} />;
      case ViewId.TOKENS:
        return <TokensView tokens={tokens} setTokens={setTokens} />;
      case ViewId.SYSTEM_STATUS:
        return <SystemStatusView components={systemComponents} />;
      case ViewId.STRATEGY_INSIGHTS:
        return <StrategyInsightsView />;
      case ViewId.RISK_MANAGEMENT:
        return <RiskManagementView isEmergencyStopActive={isEmergencyStopActive} onToggleEmergencyStop={handleToggleEmergencyStop} />;
      case ViewId.CONFIGURATION:
        return <ConfigurationView />;
      default:
        return <DashboardView kpis={kpis} recentTrades={trades} recentOpportunities={opportunities} />;
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-base-300 flex items-center justify-center">
        <div className="text-center">
          <div className="loading loading-spinner loading-lg text-primary"></div>
          <p className="mt-4 text-lg">Connecting to backend...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error && !backendHealthy) {
    return (
      <div className="min-h-screen bg-base-300 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="alert alert-error">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Backend connection failed: {error}</span>
          </div>
          <p className="mt-4 text-sm text-gray-500">
            Make sure the backend server is running on http://localhost:3001
          </p>
        </div>
      </div>
    );
  }

  return (
    <Layout currentView={currentView} onNavigate={setCurrentView}>
      {/* Connection status indicator */}
      {!isConnected && (
        <div className="alert alert-warning mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <span>WebSocket disconnected - Real-time updates unavailable</span>
        </div>
      )}
      {renderView()}
    </Layout>
  );
};

export default App;
